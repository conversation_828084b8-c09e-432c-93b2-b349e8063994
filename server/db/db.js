let mongoose = require('mongoose');
let redis = require("redis");
const { promisify } = require('util');
const config = require('../config');
const logger = require("../common/log").getLogger('db');

let countInfoSchema = new mongoose.Schema({
    name: { type: String },
    count: { type: Number, default: 0 },
});//逻辑服

let serverInfoSchema = new mongoose.Schema({
    serverId: { type: Number, default: 0 },
    userCount: { type: Number, default: 0 },
});//逻辑服

let serverLimitUserSchema = new mongoose.Schema({
    limitCount: { type: Number }
})

let userSchema = new mongoose.Schema({
    uid: { type: String, index: true, unique: true },
    signupTime: { type: Number },
    loginTime: { type: Number },
    playTime: { type: Number, default: 0 },
    updateTime: { type: Number },//存档更新时间
    logoutTime: { type: Number }, //离线时间

    avatarUrl: { type: String },//头像
    nickName: { type: String },//昵称

    diyName: { type: String },
    diyAvatar: { type: String },

    openid: { type: String },
    unionid: { type: String },
    wxAppOpenid: { type: String }, //wxApp openid
    userType: { type: String },//null表示微信小游戏  app_wx app_apple app_guest

    access_token: { type: String }, //拉取用户信息
    refresh_token: { type: String },

    forceDownload: { type: Boolean }, //强制下载存档

    ignoreBanPick: { type: Boolean }, // 无视黑名单上传存档

    serverId: { type: Number, default: 0 },//区分玩家服务器
    index_id: { type: Number, index: true }, //在全体用户中的下标

    lang: { type: String }, //使用的语言
    pay: { type: Number, default: 0 }, // 总付费 cost
    partyJoin: [], // 目前参加的派对列表
    gardenPartyJoin: [], // 目前参加的游园会列表
    todayPartyPlusJoinCnt: { type: { time: Number, cnt: Number } },
    likeNum: Number, // 好友点赞数量
});//逻辑服

let hmsAccountToken = new mongoose.Schema({
    hmsId: String,
    access_token: { type: String },
    refresh_token: { type: String },
    expire: { type: Number } // 过期
})
hmsAccountToken.index({ hmsId: 1 });

let userCancellationScheme = new mongoose.Schema({
    uid: String,
    timestamp: Number,
    state: Number, // 状态 0 可忽略行 1冷静期 2已经注销
    extends: String, // 扩展信息
})
userCancellationScheme.index({ uid: 1 });
userCancellationScheme.index({ state: 1 });


let userClientIpScheme = new mongoose.Schema({
    uid: String,
    lastLoginIp: String,// 上次成功登录的ip
    ipScore: [{
        ip: String, //ip
        score: Number // 操作得分
    }]
})
userClientIpScheme.index({ uid: 1 });


let userStatisticsSchema = new mongoose.Schema({
    unionid: { type: String, index: true },
    signupTime: { type: Number, index: true },
    loginTime: { type: Number, index: true },
    loseTime: { type: Number },//玩家存档丢失时间
    starSum: { type: Number, default: 0 },
    playTime: { type: Number, default: 0 },

    watchAdVideoTimes: { type: Number, default: 0 },
    avatarUrl: { type: String },//微信头像
    nickName: { type: String },//微信昵称

    aid: { type: String },
    scene: { type: String },
    appId: { type: String },
    model: { type: String },
    platform: { type: String },

    inviteId: { type: String },//邀请该用户的openid
    imageUrlId: { type: String },//被分享吸引时的图片id

    updateTime: { type: Number },//存档更新时间

    userType: { type: String },//null表示微信小游戏  ios android
    access_token: { type: String },
    refresh_token: { type: String },

    blackCount: { type: Number },
    bc_SpeedUp: { type: Number },
    bc_auto: { type: Number },
    forceDownload: { type: Boolean },

    channel: { type: String },//区分来源渠道

    serverId: { type: Number, default: 0 },//区分玩家服务器

    payCount: { type: Number, default: 0 },//付费金额

    markFlag: { type: Number },
    userInfoUpdateTime: { type: Number },//头像更新时间
    session_key: { type: String }, //wx的session_key，用于解密数据
    wechatCountFlag: { type: Boolean },
});//逻辑服

let gameRecordSchema = new mongoose.Schema({
    uid: { type: String, index: true, unique: true },
    gameVer: { type: String },
    recordBuff: { type: Buffer },
    checkInfo: { type: String }, //存档的校验信息，以stringify的形式存储，展开为 {hotel_1: {__data_date, __data_ver}, playTime, heart}
});//逻辑服

let gameRecordBlackListTempSchema = new mongoose.Schema({
    uid: { type: String, index: true, unique: true },
    gameVer: { type: String },
    recordBuff: { type: Buffer },
    checkInfo: { type: String },
});

let gameRecordCacheSchema = new mongoose.Schema({
    uid: { type: String, index: true, unique: true },
    gameVer: { type: String },
    recordBuff: { type: Buffer },
})

let backupUserDataSchema = new mongoose.Schema({
    uid: { type: String },
    timestamp: { type: Number },
    data: { type: String },
    reason: { type: String }
});//逻辑服

// 差异存档
let gameRecordDiffSchema = new mongoose.Schema({
    uid: { type: String },
    diff: { type: String },
    timestamp: { type: Number }
})
gameRecordDiffSchema.index({ uid: 1 })
gameRecordDiffSchema.index({ uid: 1, timestamp: 1 })

// 通货余额
let currencyBalanceSchema = new mongoose.Schema({
    uid: { type: String },
    currency: { type: String },// 类型 windmill-风车 scissor-剪刀
    balance: { type: Number },// 余额
    total: { type: Number },// 总获取
    used: { type: Number }// 已使用总额
})
currencyBalanceSchema.index({ uid: 1 })

// 通货订单
let currencyOrderSchema = new mongoose.Schema({
    uid: { type: String },
    action: { type: String },// 行为
    order: { type: String },// 订单
    extra: { type: String },// 拓展数据
    extraRes: { type: String }, //需要返回给前端的数据
    status: { type: Number },// 状态 0-未支付 1-支付成功 2-支付失败 3-已完成 4-已取消
    timestamp: { type: Number }
})
currencyOrderSchema.index({ uid: 1 })
currencyOrderSchema.index({ order: 1 })
currencyOrderSchema.index({ status: 1 })
currencyOrderSchema.index({ uid: 1, order: 1 })

let idMapSchema = new mongoose.Schema({
    tpid: { type: String, index: true, unique: true },
    uid: { type: String },
})

//账号
let accountSchema = new mongoose.Schema({
    userName: { type: String, index: true },
    password: { type: String },
    permission: { type: String, default: '000' },
    token: { type: String },
    codeCount: { type: Number, default: 0 },
});

let redeemCodeSchema = new mongoose.Schema({
    code: { type: String },
    type: { type: String }, //type1,id1,count1|type2,id2,count2
    value: { type: Number },
    date: { type: Number },
    unique: { type: Number },
    verified: { type: Boolean, default: false },
    account: { type: String },
    dispatched: { type: Boolean, default: false },
    permission: { type: Number, default: 0 },
    validTime: { type: Number } //小时   过期时间
});
redeemCodeSchema.index({ code: 1 })

let redeemCodeHistorySchema = new mongoose.Schema({
    code: { type: String },
    type: { type: String },
    value: { type: Number },
    date: { type: Number },
    unique: { type: Number },
    verified: { type: Boolean, default: false },
    account: { type: String },
    dispatched: { type: Boolean, default: false },
    permission: { type: Number, default: 0 },
    validTime: { type: Number }, //小时   过期时间
    count: { type: Number, default: 0 },
});
redeemCodeHistorySchema.index({ code: 1 })

let redeemCodePlanSchema = new mongoose.Schema({
    type: { type: String },
    name: { type: String },
})

let userRedeemCodeSchema = new mongoose.Schema({ //玩家领取过的兑换码
    uid: { type: String },
    codes: [{
        type: { type: String },
        value: { type: Number },
        timestamp: { type: Number },
    }],
});
userRedeemCodeSchema.index({ uid: 1 })

let customerCdkPlanSchema = new mongoose.Schema({ //后台奖励方案模板
    name: { type: String },
    template: { type: String },
});

let bigVersionUpdateSchema = new mongoose.Schema({
    platform: { type: String, index: true },
    version: { type: String },
    // channel: {type: String, default: "official"},
    downloadUrl: { type: String }, //下载跳转连接
    isUpdate: { type: Boolean }, //是否开启更新提醒
    force: { type: Boolean }, //强制更新
}); //逻辑服
// bigVersionUpdateSchema.index({platform: 1, channel: 1})

let hotUpdateSchema = new mongoose.Schema({
    platform: { type: String }, //android ios
    version: { type: String }, //eg: 0.0.1
    manifestUrl: { type: String },  //一般就是plantform/version/project.manifest
    packageUrl: { type: String },
    releasePercent: { type: Number, default: 0 }, //灰度百分比, 0-100
    isUpdate: { type: Boolean, default: true }, //是否开启更新
    whiteList: [{ type: String }], //灰度白名单，会优先更新到
    releaseRandom: { type: Number }, //灰度时用来随机哪些用户可以灰度
    timestamp: { type: Number },
    force: { type: Boolean }, //强制更新
    preVersion: { type: String }, //前置版本,至少大于等于这个版本才会更到version
}); //逻辑服
hotUpdateSchema.index({ platform: 1, _id: 1 })

let clientErrorSchema = new mongoose.Schema({
    uid: { type: String },
    platform: { type: String },
    version: { type: String },
    exception: { type: String },
    timestamp: { type: Number },
    type: { type: String },
    level: { type: Number },
});//统计服
clientErrorSchema.index({ version: 1 });
clientErrorSchema.index({ uid: 1 });

let whiteListSchema = new mongoose.Schema({
    uid: { type: String },
    remark: { type: String },
});
whiteListSchema.index({ uid: 1 })

let blackListSchema = new mongoose.Schema({
    uid: { type: String },
    types: [{ type: Number }],
    level: { type: Number, default: 1 },
    reason: { type: String },
    timestamp: { type: Number },
    heart: { type: Number },
    playTime: { type: Number },
    passDay: { type: Number },
    canDeblock: { type: Boolean },
});
blackListSchema.index({ uid: 1 })
blackListSchema.index({ timestamp: 1 })
blackListSchema.index({ types: 1 })
blackListSchema.index({ canDeblock: 1 })

// 仅用作标记，而不会真实封禁
let blackTagSchema = new mongoose.Schema({
    uid: { type: String },
    timestamp: { type: Number },
    cal: { type: String },
});
blackTagSchema.index({ uid: 1 })
blackTagSchema.index({ timestamp: 1 })

let blackListHistorySchema = new mongoose.Schema({
    uid: { type: String },
    types: [{ type: Number }],
    level: { type: Number, default: 1 },
    reason: { type: String },
    timestamp: { type: Number },
    heart: { type: Number },
    playTime: { type: Number },
    passDay: { type: Number },
});
blackListHistorySchema.index({ uid: 1 })

//废弃
let blackRecordSchema = new mongoose.Schema({
    uid: { type: String },
    types: [{ type: Number }],
    reason: { type: String },
    heart: { type: Number },
    playTime: { type: Number },
    passDay: { type: Number },
    timestamp: { type: Number },
});
blackRecordSchema.index({ uid: 1 })
blackRecordSchema.index({ timestamp: 1 })
blackRecordSchema.index({ heart: 1 })
blackRecordSchema.index({ playTime: 1 })
blackRecordSchema.index({ passDay: 1 })
blackRecordSchema.index({ types: 1 })

let gmSchema = new mongoose.Schema({
    uid: { type: String },
    unionid: { type: String },
    openid: { type: String },
    nickName: { type: String },
    active: { type: Boolean },
});
gmSchema.index({ uid: 1 })
gmSchema.index({ unionid: 1 })
gmSchema.index({ openid: 1 })

let loginInfoSchema = new mongoose.Schema({
    uid: { type: String },
    platform: { type: String },
    deviceId: { type: String },
    gameVer: { type: String },
    ip: { type: String },
    usedDevices: [{ type: String }],
    usedPlatforms: [{ type: String }],
})
loginInfoSchema.index({ uid: 1 })

let loginGameVersionSchema = new mongoose.Schema({
    active: { type: Boolean },
    version: { type: String },
    maxVersion: { type: String },
    samePlatformLimit: { type: Boolean }, //同平台限制
})

let dailyShareSchema = new mongoose.Schema({
    uid: { type: String },
    date: { type: Number },
    type: { type: Number },
    shareCount: { type: Number, default: 0 },
    clickCount: { type: Number, default: 0 },
    newUserCount: { type: Number, default: 0 },
    oldUserCount: { type: Number, default: 0 },
})
dailyShareSchema.index({ uid: 1 })
dailyShareSchema.index({ uid: 1, type: 1 })
dailyShareSchema.index({ uid: 1, date: 1 })
dailyShareSchema.index({ uid: 1, date: 1, type: 1 })

let shareImageSchema = new mongoose.Schema({
    imageName: { type: String, index: true },
    shareCount: { type: Number, default: 0 },
    clickCount: { type: Number, default: 0 },
    newUserCount: { type: Number, default: 0 },
    oldUserCount: { type: Number, default: 0 },
})

let feedbackOrderSchema = new mongoose.Schema({
    uid: { type: String },
    type: { type: Number, default: 0 }, //0: 一般反馈，1: 作弊反馈
    language: { type: String },
    timestamp: { type: Number },
    account: { type: String }, //客服id
    status: { type: Number, default: 0 }, //工单状态
});
feedbackOrderSchema.index({ uid: 1 })
feedbackOrderSchema.index({ timestamp: 1 })
feedbackOrderSchema.index({ type: 1 })

let feedbackMsgSchema = new mongoose.Schema({
    orderId: { type: String },
    type: { type: Number },
    content: { type: String },
    timestamp: { type: Number },
    senderType: { type: Number }, //0: 玩家发的 1：客服发的
    nickName: { type: String },
    version: { type: String },
    platform: { type: String },
})
feedbackMsgSchema.index({ orderId: 1 })
feedbackMsgSchema.index({ orderId: 1, timestamp: 1 })

let roomLikeSchema = new mongoose.Schema({
    uid: { type: String },
    roomId: { type: String },
    like: { type: Number, default: 0 },
    likeUsers: [{
        uid: { type: String },
        timestamp: { type: Number },
    }],
})
roomLikeSchema.index({ uid: 1 })
roomLikeSchema.index({ uid: 1, roomId: 1 })

let recommendSchema = new mongoose.Schema({
    uid: { type: String },
    roomId: { type: String },
    activation: [{// 活跃记录
        date: { type: String },// 日期
        count: { type: Number, default: 0 },// 活跃度
    }],
    furnitures: { type: Number, default: 0 },// 家具数量
    recommended: { type: Number, default: 0 },// 被推荐次数
    roomlike: { type: Number, default: 0 },// 被点赞次数
})
recommendSchema.index({ uid: 1, roomId: 1 })

let buildPlanLikeSchema = new mongoose.Schema({
    _pid: { type: String },// 对应buildPlanSchema的objectId
    likeUsers: [{
        uid: { type: String },
        timestamp: { type: Number },
    }],
})
buildPlanLikeSchema.index({ _pid: 1 })
// 装修方案 数据转移至 3.0
let buildPlanSchema = new mongoose.Schema({
    uid: { type: String },
    roomId: { type: String },
    activation: [{// 活跃记录
        date: { type: String },// 日期
        count: { type: Number, default: 0 },// 活跃度
    }],
    furnitures: { type: Number, default: 0 },// 家具数量
    recommended: { type: Number, default: 0 },// 被推荐次数
    roomlike: { type: Number, default: 0 },// 被点赞次数
    heart: Number, // 方案所需蜡烛
    saveInfo: String, //方案信息
    state: Number, //方案状态 0 待审核 1已精选 2 已确认 3 拒绝精选
    ts: Number, // 投稿时间戳


})
buildPlanSchema.index({ uid: 1 })
buildPlanSchema.index({ heart: 1 })
buildPlanSchema.index({ roomlike: 1 })
buildPlanSchema.index({ roomId: 1 })
buildPlanSchema.index({ state: 1 })
buildPlanSchema.index({ uid: 1, roomId: 1 })
buildPlanSchema.index({ uid: 1, state: 1 })

//趣闻
let newsSchema = new mongoose.Schema({
    next: 0,// 下一次刷新的时间
    day: Number, //趣闻所在天
    types: String, //趣闻统计类型
    rankInfo: String, //结算时的排行榜数据
})
newsSchema.index({ day: 1 });


//废弃
// let inviteSchema = new mongoose.Schema({
//     uid: {type: String},
//     type: {type: Number}, //邀请的类型，一般区分为通常邀请，各种活动邀请
//     inviteUsers: [{
//         uid: {type: String},
//         timestamp: {type: Number},
//     }],
// })
// inviteSchema.index({uid: 1})
// inviteSchema.index({uid: 1, type: 1})

let subscribeSchema = new mongoose.Schema({
    uid: { type: String },
    msgType: { type: String }, //消息类型
    template_id: { type: String }, //老字段，小程序用
    notifyTime: { type: Number }, //消息发送时间
    data: { type: String }, //序列化的数据
    userType: { type: String }, //区分用哪个平台，老字段
    platform: { type: String }, //区分用哪个平台, 新字段
    expireTime: { type: Number }, //过期时间, 默认10分钟, 超过notifyTime+expireTime还没消费的消息自动丢弃掉
    taskId: { type: String }, //用于手动推送时的标记，防止多次推送
})
subscribeSchema.index({ uid: 1 })
subscribeSchema.index({ notifyTime: 1 })

let broadcastSchema = new mongoose.Schema({
    taskId: String,
    type: String, // 全部或者个人
    notifyTime: Number, // 消息发送时间
    verLimit: String, //版本限制 type是全部时
    loginTimeLimit: String, // 登录时间限制 type是全部时
    uid: String, // type是个人时
    title: String, // type是个人时
    content: String, // type是个人时
    msgType: String,
    timestamp: Number, // 信息创建时间
    readyCount: Number, // 总人数
    finishCount: Number, // 已经成功推送的人数
    notifyDealCount: Number, // 数据已经处理的人数
    notifyCount: Number, // 推送已经处理的记录数
    docTotalCount: Number, // 拥有deviceToken的总数
    state: Number, // -> 0为每个玩家生成推送信息 -> 1等待到推送时间 -> 2推送中 -> 3推送完成  4撤销 5丢弃
})
broadcastSchema.index({ taskId: 1 })
broadcastSchema.index({ notifyTime: 1 })

let invitedSchema = new mongoose.Schema({
    uid: { type: String },
    type: { type: Number }, //邀请的类型，一般区分为通常邀请，各种活动邀请
    inviter: { type: String },
    timestamp: { type: Number },
})
invitedSchema.index({ uid: 1 })
invitedSchema.index({ inviter: 1 })
invitedSchema.index({ inviter: 1, type: 1 })

let specialAwardSchema = new mongoose.Schema({
    uid: { type: String },
    awards: [{ type: Number }],
})
specialAwardSchema.index({ uid: 1 })

let swtichSchema = new mongoose.Schema({
    type: { type: Number },
    active: { type: Boolean },
})
swtichSchema.index({ type: 1 })

let motherMsgSchema = new mongoose.Schema({
    uid: { type: String },
    content: { type: String },
    status: { type: Number },
    contentLen: { type: Number },
    timestamp: { type: Number },
})
motherMsgSchema.index({ uid: 1 })
motherMsgSchema.index({ status: 1, contentLen: 1 })

let performanceSchema = new mongoose.Schema({
    name: { type: String },
    totalTime: { type: Number },
    maxTime: { type: Number },
    maxAvgTime: { type: Number },
    count: { type: Number },
    date: { type: Number },
    reqTotalSize: { type: Number },
    repTotalSize: { type: Number },
})
performanceSchema.index({ name: 1, date: 1 })
performanceSchema.index({ date: 1 })

let applySchema = new mongoose.Schema({// 好友申请集合
    uid: { type: String },
    toUid: { type: String },
    date: { type: Number }
})
applySchema.index({ uid: 1 })
applySchema.index({ toUid: 1 })
applySchema.index({ uid: 1, toUid: 1 })

let friendSchema = new mongoose.Schema({// 好友集合
    uid: { type: String },
    toUid: { type: String },
    date: { type: Number }
})
friendSchema.index({ uid: 1 })
friendSchema.index({ toUid: 1 })
friendSchema.index({ uid: 1, toUid: 1 })

let partySchema = new mongoose.Schema({// 派对集合
    uid: String,
    partyId: Number, // 派对序列号
    time: Number, // 上一次派对创建时间
    status: Number, // 派对状态 0筹备中  1已经筹备完成  2 已经举办结束
    joiner: [], // 参与者
    overtime: Number, // 上一次派对举办时间 cd用这个时间来算
})
partySchema.index({ uid: 1 })
partySchema.index({ time: 1 })
partySchema.index({ status: 1 })

let partyPlusSchema = new mongoose.Schema({// 派对v2
    uid: { type: String, unique: 1 },
    partyId: Number,
    time: Number,
    dressUp: String,
    joiner: [{
        uid: String,
        time: Number,
        dressUp: String,
    }],
})
partyPlusSchema.index({ uid: 1 })

let friendLikeRecordSchema = new mongoose.Schema({// 好友点赞记录
    uid: { type: String, unique: 1 },
    record: [{
        uid: String,
        time: Number
    }],
})
friendLikeRecordSchema.index({ uid: 1 })

let gardenSchema = new mongoose.Schema({// 花园集合
    uid: { type: String, unique: 1 },
    list: [], // 日志列表
})
gardenSchema.index({ uid: 1 })
// 无法预估数据量，但是应该不会很大，为了安全还是加上索引
gardenSchema.index({ 'list.uid': 1 })
gardenSchema.index({ 'list.plant': 1 })
gardenSchema.index({ 'list.type': 1 })
gardenSchema.index({ 'list.time': 1 })

let gardenPartySchema = new mongoose.Schema({// 游园会
    uid: { type: String, unique: 1 },
    partyId: Number, // 派对序列号
    time: Number, // 派对创建时间
    step: Number, // 派对阶段，控制可以邀请的好友数量
    status: Number, // 派对状态 0筹备中  1已经筹备完成  2 已经举办结束
    joiner: [], // 参与者
})
gardenPartySchema.index({ uid: 1 })
gardenPartySchema.index({ time: 1 })
gardenPartySchema.index({ status: 1 })


let friendRewardSchema = new mongoose.Schema({// 好友互动奖励集合
    uid: { type: String },
    toUid: { type: String },
    date: { type: Number },
    reward: { type: String },
    type: { type: Number },
})
friendRewardSchema.index({ uid: 1 })
friendRewardSchema.index({ toUid: 1 })
friendRewardSchema.index({ uid: 1, toUid: 1 })

let favorabilitySchema = new mongoose.Schema({// 好感度集合
    uid: { type: String },
    toUid: { type: String },
    exp: { type: Number },// 好感度经验
    level: { type: Number }// 好感度等级
})
favorabilitySchema.index({ uid: 1 })
favorabilitySchema.index({ toUid: 1 })
favorabilitySchema.index({ uid: 1, toUid: 1 })

let wdWorkApplySchema = new mongoose.Schema({// 乌冬工作邀请集合
    uid: { type: String },
    toUid: { type: String },
    date: { type: Number }
})
wdWorkApplySchema.index({ uid: 1 })
wdWorkApplySchema.index({ toUid: 1 })
wdWorkApplySchema.index({ uid: 1, toUid: 1 })
// wdWorkApplySchema.index({ uid: 1, toUid: 1 })
// wdWorkApplySchema.index({ uid: 1, toUid: 1, date: 1 })

let wdWorkingSchema = new mongoose.Schema({// 乌冬工作集合
    uid: { type: String },
    toUid: { type: String },
    date: { type: Number },
    receive: { type: Number }// 是否领取过奖励
})
wdWorkingSchema.index({ uid: 1 })
wdWorkingSchema.index({ toUid: 1 })
wdWorkingSchema.index({ uid: 1, toUid: 1 })

// wdWorkingSchema.index({ uid: 1, toUid: 1 })
// wdWorkingSchema.index({ toUid: 1, date: 1 })
// wdWorkingSchema.index({ uid: 1, receive: 1 })

let expVerSchema = new mongoose.Schema({
    uid: { type: String },
    version: { type: String },
})
expVerSchema.index({ uid: 1 })

let monitorSchema = new mongoose.Schema({
    uid: { type: String },
    points: [{
        ht: { type: Number },
        bs: { type: Number },
        cd: { type: Number },
        ad: { type: Number },
        t: { type: Number },
    }],
})
monitorSchema.index({ uid: 1 })

let recordSnapshootSchema = new mongoose.Schema({
    uid: { type: Number },
    name: { type: String },
    sourceUid: { type: String },
    heart: { type: Number },
    type: { type: Number },
    createTime: { type: Number },
    recordBuff: { type: Buffer },
})
recordSnapshootSchema.index({ uid: 1 })

let ipSchema = new mongoose.Schema({
    uid: { type: String },
    ip: { type: String },
    count: { type: Number },
})
ipSchema.index({ uid: 1 })

let deviceSchema = new mongoose.Schema({
    uid: { type: String },
    deviceId: { type: String },
    count: { type: Number },
})
deviceSchema.index({ uid: 1 })

let userTokenSchema = new mongoose.Schema({
    uid: { type: String },
    token: { type: String },
    createTime: { type: Number },
    type: { type: String },
})
userTokenSchema.index({ uid: 1 })

let taskAvgSchema = new mongoose.Schema({
    key: { type: Number, index: true },
    result: { type: String },
});

let taskProgressSchema = new mongoose.Schema({
    type: { type: String, index: true },
    key: { type: String, index: true },
    done: { type: Number },
    total: { type: Number },
    result: { type: String },
    expireTime: { type: Number },
    lastRunDay: { type: Number }
});

let statUserHeartSchema = new mongoose.Schema({
    uid: { type: String },
    heart: { type: Number },
})
statUserHeartSchema.index({ uid: 1 })

let statUserGuideSchema = new mongoose.Schema({
    uid: { type: String },
    guideId: { type: Number },
    progress: { type: Number },
})
statUserGuideSchema.index({ uid: 1 })

let userIDCardSchema = new mongoose.Schema({
    uid: { type: String },
    name: { type: String }, //姓名
    idNum: { type: String }, //身份证号
    timestamp: { type: Number },
})
userIDCardSchema.index({ uid: 1 })

//记录国家法定工作日
let workDaySchema = new mongoose.Schema({
    date: { type: Number }
})

let guessAdultSchema = new mongoose.Schema({
    uid: { type: String },
    prop: { type: Number }, //可能性
})
guessAdultSchema.index({ uid: 1 })

let payOrderSchema = new mongoose.Schema({
    uid: { type: String },
    order_id: { type: String },
    platform: { type: String },
    package: { type: String },
    product: { type: String },
    token: { type: String },
    timestamp: { type: Number },
    is_test: { type: Boolean },
    stat: { type: Number },
    exception: { type: String },
    errCode: { type: Number },
    payload: { type: String }, //扩展用
})
payOrderSchema.index({ uid: 1 })
payOrderSchema.index({ order_id: 1 })

let umengSchema = new mongoose.Schema({
    eventId: { type: String },
    params: [{
        key: { type: String },
        val: { type: Number },
    }],
    date: { type: Number },
})
umengSchema.index({ eventId: 1 })
umengSchema.index({ eventId: 1, date: 1 })

let deviceTokenScheme = new mongoose.Schema({
    uid: { type: String },
    token: { type: String },
    platform: { type: String },
})
deviceTokenScheme.index({ uid: 1 });

let specialActivityScheme = new mongoose.Schema({
    uid: { type: String, index: true, unique: true }, // 玩家id
    key: { type: String }, // 活动key
    coupon: Number, // 抽奖券
    skin: [{
        id: { type: Number }, // 奖励id
        num: { type: Number }, // 奖励数量
    }], // 皮肤碎片数据
    couponPoint: Number, // 珍宝点
    videoGet: Number, // 视频彩蛋获取次数
    point: Number, // 积分
    login: Number,  // 当前任务登录数据
    up: Number, // up定向选取
    //totalLogin: Number,  // 活动期间内的登录数据 超过32天无法记录 弃用
    bigLogin: String, // 超过32天可以记录
    update: Number, // 上一次登录计数生效所在天
    task: [], // 任务数据
    heart: Number, // 增加蜡烛总数，方便计算服
    reward: [], // 获得的奖励列表
    choose: Number, // 未操作的奖励数据
    limit: [{
        id: { type: Number }, // 奖励id
        time: { type: Number }, // 时间
    }], // 兑换数据
    record: [{
        id: { type: Number }, // 抽到的奖励id
        ch: { type: Boolean }, // 是不是换成了积分
        time: { type: Number }, // 时间
    }], // 抽奖记录
    taskLimitData: [
        { group: { type: Number }, count: { type: Number } }
    ],// 任务限制数据
})
specialActivityScheme.index({ uid: 1 });
specialActivityScheme.index({ key: 1 });
specialActivityScheme.index({ uid: 1, key: 1 });

// 周年庆寄语
let yearActivityScheme = new mongoose.Schema({
    uid: String, // 玩家id
    msg: String,
    time: Number,
    tag: Boolean,
    seed: Number
})
deviceTokenScheme.index({ uid: 1, time: 1 });
deviceTokenScheme.index({ tag: 1 });

let deviceInfoSchema = new mongoose.Schema({
    uid: { type: String },
    "adid": { type: String },//广告ID,安卓是Google Advertising ID ;ios是IDFA
    "idfv": { type: String },//idfv（仅ios）
    "imei": { type: String },//国际移动设备识别码（仅安卓）
    "android_id": { type: String },//安卓id，安卓设备号（仅安卓）
    "appsflyer_id": { type: String },//appsflyer sdk得到的id
    "device_token": { type: String },//推送消息用的token
    "mac_address": { type: String },//mac地址
    "device_model": { type: String },//设备型号
    "device_name": { type: String },//设备名字
    "os_version": { type: String },//手机系统版本
    "network_type": { type: String },
    "language": { type: String },
    "app_version": { type: String },

    bundle_id: { type: String },
    server_id: { type: String }, //目前按排行榜划分的服在统计上意义不大，先统一都算成一个服
    user_id: { type: String },
    session_id: { type: String },
    user_name: { type: String },
    lv: { type: String },
    account: { type: String },
    platform: { type: String },
})
deviceInfoSchema.index({ uid: 1 });

// 装修指南2.0
let buildGuideInfoSchema = new mongoose.Schema({
    uid: { type: String },
    roomId: { type: Number },
    saveInfo: { type: String },
    heartLimit: { type: Number },
    previewUrl: { type: String },
    state: { type: Number },
    date: { type: Number }
})
buildGuideInfoSchema.index({ uid: 1 })
buildGuideInfoSchema.index({ heartLimit: 1 })
buildGuideInfoSchema.index({ state: 1 })
buildGuideInfoSchema.index({ roomId: 1 })

let roomFavSchema = new mongoose.Schema({
    _pid: { type: String },
    like: { type: Number, default: 0 },
    likeUsers: [{
        uid: { type: String },
        timestamp: { type: Number },
    }],
})
roomFavSchema.index({ _pid: 1 })
let compensationRewardScheme = new mongoose.Schema({
    uid: { type: String },
    type: { type: Number },
    reward: { type: String },
    timestamp: { type: Number },
    lang: { type: String },
    needGameVer: { type: String },
    limitTime: { type: Number }
})
// compensationRewardScheme.index({uid: 1});
//退款
let refundSchema = new mongoose.Schema({
    uid: String, //用户id
    time: Number, // 退款时间
    reason: String, // 商店返回的退款原因
    orderId: String, //订单号
    productId: String, //商品Id
    platform: String, //平台
})
refundSchema.index({ uid: 1 })
refundSchema.index({ orderId: 1 })

let payForStatShcema = new mongoose.Schema({
    storeId: Number, // 商品id
    showCount: Number,// 展示次数
    buyCount: Number,// 购买次数
})
payForStatShcema.index({ storeId: 1 })

let logStatSchema = new mongoose.Schema({
    name: String,
    desc: String,
    open: { type: Boolean, default: false },
    config: String,
    createTs: Number,
    colName: String,
})
logStatSchema.index({ name: 1 })
logStatSchema.index({ colName: 1 })


let hotUpdateStatSchema = new mongoose.Schema({
    uid: String, //标识id，用来做用户数统计
    platform: String, //平台
    curVer: String, //当前版本
    version: String, //热更之后的版本
    time: Number, //花费时间
    size: Number, //更新大小
    files: Number, //更新文件数
    isFinish: Boolean, //是否完成
})
hotUpdateStatSchema.index({ version: 1 })
hotUpdateStatSchema.index({ version: 1, time: 1 })
hotUpdateStatSchema.index({ uid: 1 })


let catchUserSchema = new mongoose.Schema({
    uid: String,
    count: Number, //需要扣除的次数
    lastEditTimeStamp: Number, //上一次进行扣除的时间
    editCount: Number,
})
catchUserSchema.index({ uid: 1 })

let roomFurnSaveSchema = new mongoose.Schema({
    originalData: String,
    planName: String,
    info: String,
    timestamp: Number
})
roomFurnSaveSchema.index({ planName: 1 });

let tempBlackTagSchema = new mongoose.Schema({
    uid: String,
    reason: String,//原因 1 上传存档时diff检测广告次数不通过 2 广告次数差异过大 3 饼干包数量不合理
    timestamp: Number,
    recordBuff: Buffer
})
tempBlackTagSchema.index({ uid: 1 });

let functionOpenSchema = new mongoose.Schema({
    functionName: String,
    isOpen: Boolean,
    extends: String
})
functionOpenSchema.index({ functionName: 1 });

let tempValidStrCheckSchema = new mongoose.Schema({
    str: String,
    p: Boolean
})
tempValidStrCheckSchema.index({ str: 1 })

let db = {
    inited: false,
    init() {
        let { mongo, redis } = config;
        if (mongo) {
            let { inactive, logic, statistics } = mongo;
            if (logic) {
                db.connect(logic);
            }
            if (inactive) {
                db.createConnectionInactive(inactive);
            }
            if (statistics) {
                db.createConnectionStat(statistics);
            }
        }

        if (redis) {
            db.connectRedis(redis);
        }
        this.inited = true
    },

    createConnectionInactive({ name, url }) {
        this.INACTIVE_DB_URL = url;

        let opt = {
            dbName: name,
            // autoIndex: false,
            useNewUrlParser: true,
            useUnifiedTopology: true,
            useFindAndModify: false,
            useCreateIndex: true,
            // 连接池配置
            maxPoolSize: 30,        // Inactive DB 连接数较少
            minPoolSize: 2,
            maxIdleTimeMS: 30000,
            waitQueueTimeoutMS: 10000,
            serverSelectionTimeoutMS: 10000,
            socketTimeoutMS: 45000,
            connectTimeoutMS: 10000,
        };

        let db = this.InactiveDB = mongoose.createConnection(this.INACTIVE_DB_URL, opt);

        db.on('connected', function () {
            logger.info(process.pid, 'InactiveDB connection open to ' + url, name);
        });
        db.on('error', function (err) {
            logger.error(process.pid, 'InactiveDB connection error ' + url, name, err);
        })
        db.on('close', function () {
            logger.info(process.pid, 'InactiveDB connection close ' + url, name);
        })
    },

    createConnectionStat({ name, url }) {
        this.Stat_DB_URL = url;

        let opt = {
            dbName: name,
            useNewUrlParser: true,
            useUnifiedTopology: true,
            useFindAndModify: false,
            useCreateIndex: true,
        };

        let db = this.statdb = mongoose.createConnection(this.Stat_DB_URL, opt);

        db.on('connected', function () {
            logger.info(process.pid, 'Statistics DB connection open to ' + url, name);
        });
        db.on('error', function (err) {
            logger.error(process.pid, 'Statistics DB connection error ' + url, name, err);
        })
        db.on('close', function () {
            logger.info(process.pid, 'Statistics DB connection close ' + url, name);
        })
    },

    connect({ url, name }) {
        this.DB_URL = url;

        let opt = {
            dbName: name,
            // autoIndex: false,
            useNewUrlParser: true,
            useUnifiedTopology: true,
            useFindAndModify: false,
            useCreateIndex: true,
            // 连接池配置
            maxPoolSize: 50,        // 最大连接数，默认100
            minPoolSize: 5,         // 最小连接数，默认0
            maxIdleTimeMS: 30000,   // 连接最大空闲时间30秒，默认0(永不关闭)
            waitQueueTimeoutMS: 10000, // 等待连接的最大时间10秒，默认0(永不超时)
            serverSelectionTimeoutMS: 10000, // 服务器选择超时时间10秒，默认30秒
            socketTimeoutMS: 45000, // Socket超时时间45秒，默认0(永不超时)
            connectTimeoutMS: 10000, // 连接超时时间10秒，默认20秒
        };

        if (this.DB_URL) {
            mongoose.connect(this.DB_URL, opt);
        }

        mongoose.connection.on('connected', function () {
            logger.info(process.pid, 'Logic DB connection open to ' + url, name);
        });
        mongoose.connection.on('error', function (err) {
            logger.error(process.pid, 'Logic DB connection error ' + url, name, err);
        })
        mongoose.connection.on('close', function () {
            logger.info(process.pid, 'Logic DB connection close ' + url, name);
        })
    },

    connectRedis({ ip, port, pwd, id }) {
        //连接Redis
        let client = redis.createClient(port, ip, { detect_buffers: true });
        this.redis = {};

        let warpPromiseRedis = () => {
            for (let key in client) {
                if (typeof client[key] == "function") {
                    this.redis[key] = promisify(client[key]).bind(client);
                } else {
                    this.redis[key] = client[key];
                }
            }
        }
        warpPromiseRedis();

        //鉴权
        if (id && pwd) {
            client.auth(id + ":" + pwd);
        }

        client.on('end', function (err) {
            logger.info(process.pid, 'redis is close', ip, port);
        });

        client.on('error', function (err) {
            logger.error(process.pid, "redis is error", ip, port, err);
        });

        client.on('connect', function () {
            logger.info(process.pid, 'redis connect success!', ip, port);
        });
    },

    close(callback) {
        if (!this.inited) {
            callback()
            return
        }
        let count = 4;
        let hanldeCallback = () => {
            count--;
            if (count == 0) {
                callback()
            }
        }

        this.redis.quit().then(hanldeCallback);
        mongoose.connection.close(hanldeCallback);
        this.statdb.close(hanldeCallback)
        this.InactiveDB.close(hanldeCallback);
    },

    getWriteFunc() {
        return ["findOneAndDelete", "findOneAndRemove", "findOneAndReplace",
            "findOneAndUpdate", "insertMany", "delete", "deleteOne", "deleteMany",
            "remove", "replaceOne", "update", "updateMany", "updateOne"]
    },

    //为了双写表的需求，这里封装一下db model层的接口；
    //创建出来的model，查询的相关接口使用mainModel来查询，写接口同时会往两张表写
    //同步写所支持的接口参考getWriteFunc；写接口仅支持同步写法
    createDouleWriteModel(mainModel, followModel) {
        if (mainModel == followModel || !followModel) {
            return mainModel;
        }
        let doubleWriteModel = {};
        let writeFunc = this.getWriteFunc();
        for (let key in mainModel) {
            if (typeof mainModel[key] != "function") continue;
            if (writeFunc.includes(key)) {
                doubleWriteModel[key] = async (...args) => {
                    followModel[key](...args)
                    return await mainModel[key](...args);
                }
            } else {
                doubleWriteModel[key] = mainModel[key].bind(mainModel)
            }
        }

        return doubleWriteModel
    },

    //逻辑服db
    createUserModel() {
        return mongoose.model('user', userSchema);
    },
    createHmsAccountTokenModel() {
        return mongoose.model('hmsAccountToken', hmsAccountToken);
    },
    createInActiveUserModel() {
        return this.InactiveDB.model('user', userSchema);
    },
    createUserCancellationModel() {
        return mongoose.model('userCancellation', userCancellationScheme);
    },
    createUserClientIpModel() {
        return mongoose.model('userClientIp', userClientIpScheme);
    },
    createGameRecordModel() {
        return mongoose.model('gameRecord', gameRecordSchema);
    },
    createGameRecordBlackListTempModel() {
        return mongoose.model('gameRecordBlack', gameRecordBlackListTempSchema);
    },
    createGameRecordCacheModel() {
        return mongoose.model('gameRecordCache', gameRecordCacheSchema);
    },
    createGameRecordDiffModel() {
        return this.statdb.model('gameRecordDiff', gameRecordDiffSchema);
    },
    createCurrencyBalanceModel() {
        return mongoose.model('currencyBalance', currencyBalanceSchema);
    },
    createCurrencyOrderModel() {
        return mongoose.model('currencyOrder', currencyOrderSchema);
    },
    createInActiveGameRecordModel() {
        return this.InactiveDB.model('gameRecord', gameRecordSchema);
    },
    createCountInfoModel() {
        return mongoose.model('countInfoModel', countInfoSchema)
    },
    createServerInfoModel() {
        return mongoose.model('serverInfoModel', serverInfoSchema)
    },
    createServerLimitUserModel() {
        return mongoose.model('serverLimitUser', serverLimitUserSchema);
    },
    createIdMapModel() {
        return mongoose.model('idMap', idMapSchema);
    },
    createHotUpdateModel() {
        return mongoose.model('hotUpdate', hotUpdateSchema);
    },
    createBigVersionUpdateModel() {
        return mongoose.model('bigVersionUpdate', bigVersionUpdateSchema);
    },
    createWhiteListModel() {
        return mongoose.model('whiteList', whiteListSchema);
    },
    createBlackListModel() {
        return mongoose.model('blackList', blackListSchema);
    },
    createBlackTagModel() {
        return mongoose.model('blackTag', blackTagSchema);
    },
    createGMModel() {
        return mongoose.model('gm', gmSchema);
    },
    createLoginInfoModel() {
        return mongoose.model('loginInfo', loginInfoSchema);
    },
    createLoginGameVersionModel() {
        return mongoose.model('loginGameVersion', loginGameVersionSchema);
    },
    createRoomLikeModel() {
        return mongoose.model('roomLike', roomLikeSchema)
    },
    createInvitedModel() {
        return mongoose.model('invited', invitedSchema)
    },
    createSubscribeModel() {
        return mongoose.model('subscribe', subscribeSchema)
    },
    createBroadcastModel() {
        return mongoose.model('broadcast', broadcastSchema)
    },
    createSpecialAwardModel() {
        return mongoose.model('specialAward', specialAwardSchema)
    },
    createSwtichModel() {
        return mongoose.model('swtich', swtichSchema)
    },
    createApplyModel() {
        return mongoose.model("applyes", applySchema)
    },
    createFriendModel() {
        return mongoose.model("friends", friendSchema)
    },
    createPartyModel() {
        return mongoose.model("partys", partySchema)
    },
    createPartyPlusModel() {
        return mongoose.model("partyPlus", partyPlusSchema)
    },
    createFriendLikeRecordModel() {
        return mongoose.model("friendLikeRecord", friendLikeRecordSchema)
    },
    createGardenModel() {
        return mongoose.model("garden", gardenSchema)
    },
    createGardenPartyModel() {
        return mongoose.model("gardenParty", gardenPartySchema)
    },
    createFriendRewardModel() {
        return mongoose.model("friendRewards", friendRewardSchema)
    },
    createFavorabilityModel() {
        return mongoose.model("favorabilitys", favorabilitySchema)
    },
    createWdWorkApplyModel() {
        return mongoose.model("wdWorkApplyes", wdWorkApplySchema)
    },
    createWdWorkingModel() {
        return mongoose.model("wdWorking", wdWorkingSchema)
    },
    createRecommendModel() {
        return mongoose.model('recommend', recommendSchema)
    },
    createBuildPlanModel() {
        return mongoose.model('buildPlan', buildPlanSchema)
    },
    createBuildPlanLikeModel() {
        return mongoose.model('buildPlanLike', buildPlanLikeSchema);
    },
    createNewsModel() {
        return mongoose.model('news', newsSchema);
    },
    createUserRedeemCodeModel() {
        return mongoose.model('userRedeemCode', userRedeemCodeSchema);
    },
    createUserTokenModel() {
        return mongoose.model('userToken', userTokenSchema);
    },
    createUserIDCardModel() {
        return mongoose.model('userIDCard', userIDCardSchema);
    },
    createWorkDayModel() {
        return mongoose.model('workDay', workDaySchema);
    },
    createGuessAdultModel() {
        return mongoose.model('guessAdult', guessAdultSchema);
    },
    createPayOrderModel() {
        return mongoose.model('payOrder', payOrderSchema)
    },
    createDeviceTokenOld() {
        return mongoose.model("deviceToken", deviceTokenScheme);
    },
    createSpecialActivityModel() {
        return mongoose.model("specialActivity", specialActivityScheme);
    },
    createYearActivityModel() {
        return mongoose.model("yearActivity", yearActivityScheme);
    },
    createDeviceToken() {
        return mongoose.model("deviceTokenNew", deviceTokenScheme);
    },
    createBuildGuideInfoModel() {
        return mongoose.model("buildGuide", buildGuideInfoSchema);
    },
    createRoomFavModel() {
        return mongoose.model("roomFav", roomFavSchema);
    },
    createCompensationReward() {
        return mongoose.model("compensationReward", compensationRewardScheme);
    },
    createFunctionOpenModel() {
        return mongoose.model("functionOpen", functionOpenSchema);
    },
    createTempValidStrCheckModel() {
        return mongoose.model("tempValidStrCheck", tempValidStrCheckSchema);
    },

    // 统计服db，逻辑服db更新时需要同步
    createStatUserModel() {
        return this.statdb.model('user', userSchema);
    },
    createCustomerCdkPlanModel() {
        return this.statdb.model('customerCdkPlan', customerCdkPlanSchema);
    },
    createAccountModel() {
        return this.statdb.model('account', accountSchema);
    },
    createClientErrorModel() {
        return this.statdb.model("clientError", clientErrorSchema);
    },
    createRedeemCodeModel() {
        return this.statdb.model('redeemCode', redeemCodeSchema);
    },
    createRedeemCodeHistoryModel() {
        return this.statdb.model('redeemCodeHistory', redeemCodeHistorySchema);
    },
    createRedeemCodePlanModel() {
        return this.statdb.model('redeemCodePlan', redeemCodePlanSchema);
    },
    createDailyShareModel() {
        return this.statdb.model('dailyShare', dailyShareSchema);
    },
    createShareImageModel() {
        return this.statdb.model("shareImage", shareImageSchema)
    },
    createMotherMsgModel() {
        return this.statdb.model("motherMsg", motherMsgSchema)
    },
    createPerformanceModel() {
        return this.statdb.model("performance", performanceSchema)
    },
    createExpVerModel() {
        return this.statdb.model("expVer", expVerSchema)
    },
    createBlackRecordModel() {
        return this.statdb.model('blackRecord', blackRecordSchema);
    },
    createFeedbackOrderModel() {
        return this.statdb.model('feedbackOrder', feedbackOrderSchema);
    },
    createFeedbackMsgModel() {
        return this.statdb.model('feedbackMsg', feedbackMsgSchema);
    },
    createMonitorModel() {
        return this.statdb.model('monitor', monitorSchema);
    },
    createRecordSnapshootModel() {
        return this.statdb.model('recordSnapshoot', recordSnapshootSchema);
    },
    createIpModel() {
        return this.statdb.model('ip', ipSchema);
    },
    createDeviceModel() {
        return this.statdb.model('device', deviceSchema);
    },
    createBlackListHistoryModel() {
        return this.statdb.model("blackListHistory", blackListHistorySchema)
    },
    createTaskAvgModel() {
        return this.statdb.model("taskAvg", taskAvgSchema);
    },
    createTaskProgressModel() {
        return this.statdb.model("taskProgress", taskProgressSchema)
    },
    createStatUserHeartModel() {
        return this.statdb.model("statUserHeart", statUserHeartSchema)
    },
    createStatUserGuideModel() {
        return this.statdb.model("statUserGuide", statUserGuideSchema)
    },
    createUmengModel() {
        return this.statdb.model("umeng", umengSchema)
    },
    createDeviceInfoModel() {
        return this.statdb.model("deviceInfo", deviceInfoSchema)
    },
    createCompensationRewardHistory() {
        return this.statdb.model("compensationRewardHistory", compensationRewardScheme);
    },
    createBackupUserDataModel() {
        return this.statdb.model('backupUserData', backupUserDataSchema);
    },
    createRefundModel() {
        return this.statdb.model('refund', refundSchema);
    },
    createPayForStatModel() {
        return this.statdb.model('payForStat', payForStatShcema);
    },
    createLogStatModel() {
        return this.statdb.model('logStat', logStatSchema);
    },
    createHotUpdateStatModel() {
        return this.statdb.model('hotUpdateStat', hotUpdateStatSchema);
    },
    createTempBlackTagModel() {
        return this.statdb.model('tempBlackTag', tempBlackTagSchema);
    },
    createRoomFurnSaveModel() {
        return this.statdb.model('roomFurnSave', roomFurnSaveSchema);
    },
    createStatGameRecordModel() {
        return this.statdb.model('gameRecord', gameRecordSchema);
    },
    createCatchUserModel() {
        return mongoose.model("catchUser", catchUserSchema)
    },

};
module.exports = db;
